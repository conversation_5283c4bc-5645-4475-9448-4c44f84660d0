"""
Test for the page callback functionality in XApiClient.
"""

import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Import the XApiClient class
from sources.x.api_client import XApiClient
from core.utils.logging import get_logger

logger = get_logger(__name__)


class TestPageCallback(unittest.TestCase):
    """Test the page callback functionality in XApiClient."""

    @patch('requests.get')
    def test_page_callback(self, mock_get):
        """Test that the page callback is called after each page is fetched."""
        # Mock the API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {
            'x-rate-limit-limit': '100',
            'x-rate-limit-remaining': '99',
            'x-rate-limit-reset': '1609459200'
        }

        # Create different responses for different requests
        def mock_response_func(*args, **kwargs):
            response = MagicMock()
            response.status_code = 200
            response.headers = {
                'x-rate-limit-limit': '100',
                'x-rate-limit-remaining': '99',
                'x-rate-limit-reset': '1609459200'
            }

            # Check if this is a pagination request (has pagination_token in params)
            params = kwargs.get('params', {})
            if 'pagination_token' in params:
                # Second page response
                response.json.return_value = {
                    'data': [{'id': '2', 'text': 'Tweet 2', 'created_at': datetime.now().isoformat() + 'Z'}],
                    'meta': {
                        'newest_id': '2',
                        'oldest_id': '2',
                        'next_token': None  # No more pages
                    },
                    'includes': {'users': []}
                }
            else:
                # First page response or rate limit check
                response.json.return_value = {
                    'data': [{'id': '1', 'text': 'Tweet 1', 'created_at': datetime.now().isoformat() + 'Z'}],
                    'meta': {
                        'newest_id': '1',
                        'oldest_id': '1',
                        'next_token': 'next_token_1'
                    },
                    'includes': {'users': []}
                }

            return response

        # Use the dynamic mock function
        mock_get.side_effect = mock_response_func

        # Create a mock callback
        callback_data = []

        def mock_callback(metadata, tweets, page_count, next_token):
            callback_data.append({
                'page_count': page_count,
                'tweets_count': len(tweets) if tweets else 0,
                'next_token': next_token
            })

        # Create the client and call get_all_list_tweets with the callback
        client = XApiClient(bearer_token='test_token')
        tweets, metadata = client.get_all_list_tweets(
            list_id='test_list',
            max_results=10,
            max_pages=2,
            page_callback=mock_callback
        )

        # Verify the callback was called for each page
        self.assertEqual(len(callback_data), 2)  # 2 pages

        # First page
        self.assertEqual(callback_data[0]['page_count'], 1)
        self.assertEqual(callback_data[0]['tweets_count'], 1)
        self.assertEqual(callback_data[0]['next_token'], 'next_token_1')

        # Second page
        self.assertEqual(callback_data[1]['page_count'], 2)
        self.assertEqual(callback_data[1]['tweets_count'], 1)
        self.assertEqual(callback_data[1]['next_token'], None)


if __name__ == '__main__':
    unittest.main()
