from pydantic import BaseModel, Field, HttpUrl, ConfigDict
from datetime import datetime, timezone
from typing import Optional, List, Tuple  # Added Tuple import

from core.enums import RelevanceStatus
from core.interfaces.llm_preparable_interface import LLMPreparable


class NewsItem(BaseModel, LLMPreparable):
    """Model for representing a news item."""
    title: str
    link: HttpUrl
    published_at: datetime
    summary: str
    publisher: str = Field(default="Unknown")
    source: str = Field(default="google_news_rss")
    fetched_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    relevance: Optional[RelevanceStatus] = None
    is_time_sensitive: bool = Field(default=False)
    timeliness_reason: Optional[str] = None
    
    # Deduplication tracking fields
    is_duplicate: bool = Field(default=False)
    duplicate_of_url: Optional[str] = None
    duplicate_of_item_type: Optional[str] = None  # 'news_item' or 'tweet_item'
    duplicate_of_item_id: Optional[str] = None
    deduplication_reason: Optional[str] = None

    model_config = ConfigDict(populate_by_name=True)  # Allows using field names during initialization

    def get_llm_input(self) -> Tuple[str, str]:
        """Returns (title, content_body) for LLM evaluation."""
        return (self.title, self.summary)


class TweetItem(BaseModel, LLMPreparable):
    """Model for representing a tweet item fetched from X API."""
    # Core tweet data
    tweet_id: str # Primary field for tweet ID, no alias
    text: str
    created_at: datetime
    fetched_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # User information
    author_id: str
    author_username: Optional[str] = None
    author_name: Optional[str] = None

    # Tweet type flags
    is_retweet: bool = Field(default=False)  # Standard retweet without commentary
    is_quote: bool = Field(default=False)    # Retweet with commentary (quote tweet)
    is_reply: bool = Field(default=False)    # Reply to another tweet
    has_commentary: bool = Field(default=False)  # Indicates if the tweet has original commentary

    # Related tweet IDs
    in_reply_to_tweet_id: Optional[str] = None
    in_reply_to_user_id: Optional[str] = None
    quoted_tweet_id: Optional[str] = None
    retweeted_tweet_id: Optional[str] = None

    # URLs and media
    urls: List[HttpUrl] = Field(default_factory=list)
    hashtags: List[str] = Field(default_factory=list)
    mentions: List[str] = Field(default_factory=list)

    # Individual engagement metrics
    like_count: int = Field(default=0)
    retweet_count: int = Field(default=0)
    reply_count: int = Field(default=0)
    quote_count: int = Field(default=0)
    impression_count: Optional[int] = None
    bookmark_count: Optional[int] = None

    # Processing fields
    source: str = Field(default="x_list_scraper")
    relevance: Optional[RelevanceStatus] = None
    is_time_sensitive: bool = Field(default=False)
    timeliness_reason: Optional[str] = None
    included_in_newsletter: bool = Field(default=False)
    processed_at: Optional[datetime] = None
    notes: Optional[str] = None
    
    # Deduplication tracking fields
    is_duplicate: bool = Field(default=False)
    duplicate_of_url: Optional[str] = None
    duplicate_of_item_type: Optional[str] = None  # 'news_item' or 'tweet_item'
    duplicate_of_item_id: Optional[str] = None
    deduplication_reason: Optional[str] = None

    model_config = ConfigDict(populate_by_name=True)  # Allows using field names during initialization

    def get_llm_input(self) -> Tuple[str, str]:
        """Returns (title, content_body) for LLM evaluation."""
        author = self.author_username or self.author_id or "unknown_user"
        title = f"Tweet from @{author}"
        content_body = self.text
        return (title, content_body)